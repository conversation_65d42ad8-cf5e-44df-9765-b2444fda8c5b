/**
 * Player Perspective Helper Functions
 * 
 * These functions provide the core logic for converting between the underlying
 * player1/player2 data structure and the perspective-based currentPlayer/opponent
 * terminology that's more intuitive for users.
 */

export type PlayerPosition = 'player1' | 'player2';
export type PlayerPerspective = 'currentPlayer' | 'opponent';

/**
 * Determines the player key (player1 or player2) for a given perspective
 * based on the current user's position in the match.
 * 
 * @param perspective - Whether we want the current player or opponent
 * @param userPosition - The current user's position (player1 or player2)
 * @returns The corresponding player key
 */
export function getPlayerKey(
  perspective: PlayerPerspective,
  userPosition: PlayerPosition
): PlayerPosition {
  if (perspective === 'currentPlayer') {
    return userPosition;
  } else {
    // opponent
    return userPosition === 'player1' ? 'player2' : 'player1';
  }
}

/**
 * Gets the player key for the current player based on user position.
 * 
 * @param userPosition - The current user's position (player1 or player2)
 * @returns The player key for the current player
 */
export function getCurrentPlayerKey(userPosition: PlayerPosition): PlayerPosition {
  return getPlayerKey('currentPlayer', userPosition);
}

/**
 * Gets the player key for the opponent based on user position.
 * 
 * @param userPosition - The current user's position (player1 or player2)
 * @returns The player key for the opponent
 */
export function getOpponentKey(userPosition: PlayerPosition): PlayerPosition {
  return getPlayerKey('opponent', userPosition);
}

/**
 * Checks if a given player ID represents the current player.
 * 
 * @param playerId - The player ID to check (player1 or player2)
 * @param userPosition - The current user's position (player1 or player2)
 * @returns True if the player ID represents the current player
 */
export function isCurrentPlayer(
  playerId: PlayerPosition,
  userPosition: PlayerPosition
): boolean {
  return playerId === userPosition;
}

/**
 * Converts a perspective-based player reference to the underlying player key.
 * This is useful for commands and API calls that still use player1/player2.
 * 
 * @param perspective - The perspective-based player reference
 * @param userPosition - The current user's position
 * @returns The underlying player key
 */
export function perspectiveToPlayerKey(
  perspective: PlayerPerspective,
  userPosition: PlayerPosition
): PlayerPosition {
  return getPlayerKey(perspective, userPosition);
}

/**
 * Converts a player key to a perspective-based reference.
 * This is useful for displaying data in a user-friendly way.
 * 
 * @param playerKey - The underlying player key (player1 or player2)
 * @param userPosition - The current user's position
 * @returns The perspective-based player reference
 */
export function playerKeyToPerspective(
  playerKey: PlayerPosition,
  userPosition: PlayerPosition
): PlayerPerspective {
  return isCurrentPlayer(playerKey, userPosition) ? 'currentPlayer' : 'opponent';
}

/**
 * Gets the board key for a given perspective.
 * This is used for accessing board data in the game state.
 * 
 * @param perspective - The perspective (currentPlayer or opponent)
 * @param userPosition - The current user's position
 * @returns The board key (e.g., 'player1Board', 'player2Board')
 */
export function getBoardKey(
  perspective: PlayerPerspective,
  userPosition: PlayerPosition
): string {
  const playerKey = getPlayerKey(perspective, userPosition);
  return `${playerKey}Board`;
}

/**
 * Gets the current player's board key.
 * 
 * @param userPosition - The current user's position
 * @returns The current player's board key
 */
export function getCurrentPlayerBoardKey(userPosition: PlayerPosition): string {
  return getBoardKey('currentPlayer', userPosition);
}

/**
 * Gets the opponent's board key.
 * 
 * @param userPosition - The current user's position
 * @returns The opponent's board key
 */
export function getOpponentBoardKey(userPosition: PlayerPosition): string {
  return getBoardKey('opponent', userPosition);
}
