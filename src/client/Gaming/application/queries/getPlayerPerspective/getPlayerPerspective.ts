import {RootState} from '@/src/client/Shared/store/appStore/rootState';
import {PlacedCard, PlayerBoard} from '@/src/client/Gaming/domain/GameBoard/gameBoardReducer';
import {
  PlayerPosition,
  getCurrentPlayerKey,
  getOpponentKey,
  getCurrentPlayerBoardKey,
  getOpponentBoardKey,
} from '@/src/client/Gaming/application/helpers/playerPerspective';

/**
 * Perspective-Based Player Hand Selectors
 * 
 * These selectors provide currentPlayer/opponent data instead of player1/player2,
 * making the code more intuitive from each user's perspective.
 */

/**
 * Gets the current player's hand cards.
 * 
 * @param state - The Redux root state
 * @param userPosition - The current user's position in the match
 * @returns Array of card IDs in the current player's hand
 */
export const getCurrentPlayerHand = (
  state: RootState,
  userPosition: PlayerPosition
): string[] => {
  const playerKey = getCurrentPlayerKey(userPosition);
  if (playerKey === 'player1') {
    return state.playerHands.player1;
  } else {
    // For player2, we only have count, so return empty array
    // This maintains consistency with the current system where player2 hand is hidden
    return [];
  }
};

/**
 * Gets the opponent's hand card count.
 * 
 * @param state - The Redux root state
 * @param userPosition - The current user's position in the match
 * @returns Number of cards in the opponent's hand
 */
export const getOpponentHandCount = (
  state: RootState,
  userPosition: PlayerPosition
): number => {
  const opponentKey = getOpponentKey(userPosition);
  if (opponentKey === 'player1') {
    return state.playerHands.player1.length;
  } else {
    return state.playerHands.player2Count;
  }
};

/**
 * Gets the current player's hand card count.
 * 
 * @param state - The Redux root state
 * @param userPosition - The current user's position in the match
 * @returns Number of cards in the current player's hand
 */
export const getCurrentPlayerHandCount = (
  state: RootState,
  userPosition: PlayerPosition
): number => {
  const playerKey = getCurrentPlayerKey(userPosition);
  if (playerKey === 'player1') {
    return state.playerHands.player1.length;
  } else {
    return state.playerHands.player2Count;
  }
};

/**
 * Perspective-Based Game Board Selectors
 */

/**
 * Gets the current player's board.
 * 
 * @param state - The Redux root state
 * @param userPosition - The current user's position in the match
 * @returns The current player's board
 */
export const getCurrentPlayerBoard = (
  state: RootState,
  userPosition: PlayerPosition
): PlayerBoard => {
  const boardKey = getCurrentPlayerBoardKey(userPosition) as keyof typeof state.gameBoard;
  return state.gameBoard[boardKey] as PlayerBoard;
};

/**
 * Gets the opponent's board.
 * 
 * @param state - The Redux root state
 * @param userPosition - The current user's position in the match
 * @returns The opponent's board
 */
export const getOpponentBoard = (
  state: RootState,
  userPosition: PlayerPosition
): PlayerBoard => {
  const boardKey = getOpponentBoardKey(userPosition) as keyof typeof state.gameBoard;
  return state.gameBoard[boardKey] as PlayerBoard;
};

/**
 * Gets the current player's first row.
 * 
 * @param state - The Redux root state
 * @param userPosition - The current user's position in the match
 * @returns The current player's first row
 */
export const getCurrentPlayerFirstRow = (
  state: RootState,
  userPosition: PlayerPosition
) => {
  return getCurrentPlayerBoard(state, userPosition).firstRow;
};

/**
 * Gets the current player's second row.
 * 
 * @param state - The Redux root state
 * @param userPosition - The current user's position in the match
 * @returns The current player's second row
 */
export const getCurrentPlayerSecondRow = (
  state: RootState,
  userPosition: PlayerPosition
) => {
  return getCurrentPlayerBoard(state, userPosition).secondRow;
};

/**
 * Gets the opponent's first row.
 * 
 * @param state - The Redux root state
 * @param userPosition - The current user's position in the match
 * @returns The opponent's first row
 */
export const getOpponentFirstRow = (
  state: RootState,
  userPosition: PlayerPosition
) => {
  return getOpponentBoard(state, userPosition).firstRow;
};

/**
 * Gets the opponent's second row.
 * 
 * @param state - The Redux root state
 * @param userPosition - The current user's position in the match
 * @returns The opponent's second row
 */
export const getOpponentSecondRow = (
  state: RootState,
  userPosition: PlayerPosition
) => {
  return getOpponentBoard(state, userPosition).secondRow;
};

/**
 * Gets a placed card from the current player's board.
 * 
 * @param state - The Redux root state
 * @param userPosition - The current user's position in the match
 * @param rowType - The row type ('first' or 'second')
 * @param slotIndex - The slot index
 * @returns The placed card or null if slot is empty
 */
export const getCurrentPlayerPlacedCard = (
  state: RootState,
  userPosition: PlayerPosition,
  rowType: 'first' | 'second',
  slotIndex: number
): PlacedCard | null => {
  const board = getCurrentPlayerBoard(state, userPosition);
  const row = rowType === 'first' ? board.firstRow : board.secondRow;
  return slotIndex >= 0 && slotIndex < row.length ? row[slotIndex] : null;
};

/**
 * Gets a placed card from the opponent's board.
 * 
 * @param state - The Redux root state
 * @param userPosition - The current user's position in the match
 * @param rowType - The row type ('first' or 'second')
 * @param slotIndex - The slot index
 * @returns The placed card or null if slot is empty
 */
export const getOpponentPlacedCard = (
  state: RootState,
  userPosition: PlayerPosition,
  rowType: 'first' | 'second',
  slotIndex: number
): PlacedCard | null => {
  const board = getOpponentBoard(state, userPosition);
  const row = rowType === 'first' ? board.firstRow : board.secondRow;
  return slotIndex >= 0 && slotIndex < row.length ? row[slotIndex] : null;
};

/**
 * Checks if a slot is empty on the current player's board.
 * 
 * @param state - The Redux root state
 * @param userPosition - The current user's position in the match
 * @param rowType - The row type ('first' or 'second')
 * @param slotIndex - The slot index
 * @returns True if the slot is empty
 */
export const isCurrentPlayerSlotEmpty = (
  state: RootState,
  userPosition: PlayerPosition,
  rowType: 'first' | 'second',
  slotIndex: number
): boolean => {
  return getCurrentPlayerPlacedCard(state, userPosition, rowType, slotIndex) === null;
};

/**
 * Checks if a slot is empty on the opponent's board.
 * 
 * @param state - The Redux root state
 * @param userPosition - The current user's position in the match
 * @param rowType - The row type ('first' or 'second')
 * @param slotIndex - The slot index
 * @returns True if the slot is empty
 */
export const isOpponentSlotEmpty = (
  state: RootState,
  userPosition: PlayerPosition,
  rowType: 'first' | 'second',
  slotIndex: number
): boolean => {
  return getOpponentPlacedCard(state, userPosition, rowType, slotIndex) === null;
};
