import {render, screen, cleanup} from '@testing-library/react';
import {Provider} from 'react-redux';
import {createTestingStore} from '@/src/client/Shared/testing/store/createTestingStore';

vi.mock('@/src/client/Gaming/infrastructure/components/app/3d/Card/Card', () => ({
  __esModule: true,
  default: ({visibleHeight}: {visibleHeight?: number}) => (
    <div data-testid="card" data-visibleheight={(visibleHeight ?? 0).toString()} />
  ),
}));

vi.mock('@/src/client/Gaming/infrastructure/components/app/3d/PreloadedCard/PreloadedCard', () => ({
  __esModule: true,
  default: ({visibleHeight}: {visibleHeight?: number}) => (
    <div data-testid="card" data-visibleheight={(visibleHeight ?? 0).toString()} />
  ),
}));

vi.mock('@/src/client/Gaming/infrastructure/components/app/3d/EmptySlot/EmptySlot', () => ({
  __esModule: true,
  default: () => (
    <div data-testid="empty-slot" />
  ),
}));

vi.mock('@/src/client/Gaming/infrastructure/components/app/3d/TextureCache/TextureCache', () => ({
  useTextureCache: () => ({
    getTexture: () => null,
    backTexture: null,
  }),
}));

const PLAYER_ROWS = [
  '@/src/client/Gaming/infrastructure/components/app/3d/Player1Rows/Player1FirstRow',
  '@/src/client/Gaming/infrastructure/components/app/3d/Player1Rows/Player1SecondRow',
  '@/src/client/Gaming/infrastructure/components/app/3d/Player2Rows/Player2FirstRow',
  '@/src/client/Gaming/infrastructure/components/app/3d/Player2Rows/Player2SecondRow',
] as const;

describe('PlayerAreas', () => {
  describe('When rendering an area', () => {
    it('should display empty slots when no cards are placed', async () => {
      // Arrange
      const components = await Promise.all(
        PLAYER_ROWS.map((path) => import(path).then((m) => m.default)),
      );
      const store = createTestingStore({
        playerHands: {player1: ['1'], player2Count: 1},
      });

      // Act
      const renders = components.map((Component) =>
        render(
          <Provider store={store}>
            <Component />
          </Provider>
        )
      );

      // Assert
      const emptySlots = screen.getAllByTestId('empty-slot');
      expect(emptySlots.length).toBeGreaterThan(0);

      renders.forEach(({unmount}) => unmount());
      cleanup();
    });
  });
});
