'use client';

import React, {createContext, useContext, ReactNode} from 'react';
import {useSelector} from 'react-redux';
import {useCurrentPlayer} from '@/src/client/Gaming/infrastructure/hooks/useCurrentPlayer/useCurrentPlayer';
import {PlayerPosition} from '@/src/client/Gaming/application/helpers/playerPerspective';
import {
  getCurrentPlayerHand,
  getOpponentHandCount,
  getCurrentPlayerHandCount,
  getCurrentPlayerBoard,
  getOpponentBoard,
  getCurrentPlayerFirstRow,
  getCurrentPlayerSecondRow,
  getOpponentFirstRow,
  getOpponentSecondRow,
  getCurrentPlayerPlacedCard,
  getOpponentPlacedCard,
  isCurrentPlayerSlotEmpty,
  isOpponentSlotEmpty,
} from '@/src/client/Gaming/application/queries/getPlayerPerspective/getPlayerPerspective';
import {RootState} from '@/src/client/Shared/store/appStore/rootState';
import {PlacedCard, PlayerBoard} from '@/src/client/Gaming/domain/GameBoard/gameBoardReducer';

/**
 * Player Perspective Context
 * 
 * This context provides perspective-based data and functions to components,
 * eliminating the need for components to know about player1/player2 terminology.
 * Everything is relative to the current user's perspective.
 */

interface PlayerPerspectiveContextValue {
  // User position and loading state
  userPosition: PlayerPosition | undefined;
  isLoading: boolean;
  
  // Hand data
  currentPlayerHand: string[];
  opponentHandCount: number;
  currentPlayerHandCount: number;
  
  // Board data
  currentPlayerBoard: PlayerBoard | undefined;
  opponentBoard: PlayerBoard | undefined;
  currentPlayerFirstRow: (PlacedCard | null)[] | undefined;
  currentPlayerSecondRow: (PlacedCard | null)[] | undefined;
  opponentFirstRow: (PlacedCard | null)[] | undefined;
  opponentSecondRow: (PlacedCard | null)[] | undefined;
  
  // Helper functions
  getCurrentPlayerPlacedCard: (rowType: 'first' | 'second', slotIndex: number) => PlacedCard | null;
  getOpponentPlacedCard: (rowType: 'first' | 'second', slotIndex: number) => PlacedCard | null;
  isCurrentPlayerSlotEmpty: (rowType: 'first' | 'second', slotIndex: number) => boolean;
  isOpponentSlotEmpty: (rowType: 'first' | 'second', slotIndex: number) => boolean;
}

const PlayerPerspectiveContext = createContext<PlayerPerspectiveContextValue | undefined>(undefined);

interface PlayerPerspectiveProviderProps {
  children: ReactNode;
  matchId: string | undefined;
}

export const PlayerPerspectiveProvider: React.FC<PlayerPerspectiveProviderProps> = ({
  children,
  matchId,
}) => {
  const {currentPlayerPosition, isLoading} = useCurrentPlayer(matchId);
  const state = useSelector((state: RootState) => state);
  
  // If we don't have user position yet, provide loading state
  if (isLoading || !currentPlayerPosition) {
    const loadingValue: PlayerPerspectiveContextValue = {
      userPosition: undefined,
      isLoading: true,
      currentPlayerHand: [],
      opponentHandCount: 0,
      currentPlayerHandCount: 0,
      currentPlayerBoard: undefined,
      opponentBoard: undefined,
      currentPlayerFirstRow: undefined,
      currentPlayerSecondRow: undefined,
      opponentFirstRow: undefined,
      opponentSecondRow: undefined,
      getCurrentPlayerPlacedCard: () => null,
      getOpponentPlacedCard: () => null,
      isCurrentPlayerSlotEmpty: () => true,
      isOpponentSlotEmpty: () => true,
    };
    
    return (
      <PlayerPerspectiveContext.Provider value={loadingValue}>
        {children}
      </PlayerPerspectiveContext.Provider>
    );
  }
  
  // Create perspective-based data using selectors
  const userPosition = currentPlayerPosition;
  const currentPlayerHand = getCurrentPlayerHand(state, userPosition);
  const opponentHandCount = getOpponentHandCount(state, userPosition);
  const currentPlayerHandCount = getCurrentPlayerHandCount(state, userPosition);
  const currentPlayerBoard = getCurrentPlayerBoard(state, userPosition);
  const opponentBoard = getOpponentBoard(state, userPosition);
  const currentPlayerFirstRow = getCurrentPlayerFirstRow(state, userPosition);
  const currentPlayerSecondRow = getCurrentPlayerSecondRow(state, userPosition);
  const opponentFirstRow = getOpponentFirstRow(state, userPosition);
  const opponentSecondRow = getOpponentSecondRow(state, userPosition);
  
  // Create helper functions that use the current user position
  const getCurrentPlayerPlacedCardHelper = (rowType: 'first' | 'second', slotIndex: number) =>
    getCurrentPlayerPlacedCard(state, userPosition, rowType, slotIndex);
    
  const getOpponentPlacedCardHelper = (rowType: 'first' | 'second', slotIndex: number) =>
    getOpponentPlacedCard(state, userPosition, rowType, slotIndex);
    
  const isCurrentPlayerSlotEmptyHelper = (rowType: 'first' | 'second', slotIndex: number) =>
    isCurrentPlayerSlotEmpty(state, userPosition, rowType, slotIndex);
    
  const isOpponentSlotEmptyHelper = (rowType: 'first' | 'second', slotIndex: number) =>
    isOpponentSlotEmpty(state, userPosition, rowType, slotIndex);
  
  const value: PlayerPerspectiveContextValue = {
    userPosition,
    isLoading: false,
    currentPlayerHand,
    opponentHandCount,
    currentPlayerHandCount,
    currentPlayerBoard,
    opponentBoard,
    currentPlayerFirstRow,
    currentPlayerSecondRow,
    opponentFirstRow,
    opponentSecondRow,
    getCurrentPlayerPlacedCard: getCurrentPlayerPlacedCardHelper,
    getOpponentPlacedCard: getOpponentPlacedCardHelper,
    isCurrentPlayerSlotEmpty: isCurrentPlayerSlotEmptyHelper,
    isOpponentSlotEmpty: isOpponentSlotEmptyHelper,
  };
  
  return (
    <PlayerPerspectiveContext.Provider value={value}>
      {children}
    </PlayerPerspectiveContext.Provider>
  );
};

/**
 * Hook to use the Player Perspective Context.
 * 
 * @returns The perspective context value
 * @throws Error if used outside of PlayerPerspectiveProvider
 */
export const usePlayerPerspective = (): PlayerPerspectiveContextValue => {
  const context = useContext(PlayerPerspectiveContext);
  if (context === undefined) {
    throw new Error('usePlayerPerspective must be used within a PlayerPerspectiveProvider');
  }
  return context;
};

/**
 * Hook to get just the user position, useful for components that need
 * to know the user's position but don't need all the perspective data.
 * 
 * @returns The user's position and loading state
 */
export const useUserPosition = (): {userPosition: PlayerPosition | undefined; isLoading: boolean} => {
  const {userPosition, isLoading} = usePlayerPerspective();
  return {userPosition, isLoading};
};
