'use client';

import {FC, useState} from 'react';
import {useLoader} from '@react-three/fiber';
import {TextureLoader} from 'three';
import {useDispatch} from 'react-redux';
import {AppDispatch} from '@/src/client/Shared/store/appStore/appDispatch';
import {showZoomedCard} from '@/src/client/Gaming/application/commands/showZoomedCard/showZoomedCard';
import {usePlayerPerspective} from '@/src/client/Gaming/infrastructure/context/PlayerPerspectiveContext';

type Props = {
  onCardSelect?: (cardId: string) => void;
};

const RADIUS = 12.5;
const scale = 1;
const CARD_WIDTH = 2 * scale;
const CARD_HEIGHT = 3 * scale;

const CurrentPlayerHand: FC<Props> = ({onCardSelect}) => {
  const {currentPlayerHand, isLoading} = usePlayerPerspective();
  const dispatch = useDispatch<AppDispatch>();
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);

  // Don't render anything while loading or if no cards
  if (isLoading || currentPlayerHand.length === 0) {
    return null;
  }

  const cardIds = currentPlayerHand;
  const textures = useLoader(TextureLoader, cardIds);

  const cardCount = cardIds.length;
  const middleIndex = (cardCount - 1) / 2;

  const maxArc = Math.PI / 2.5;
  const minArc = Math.PI / 10;
  const ARC_WIDTH = (() => {
    if (cardCount === 2) return 0.15;
    if (cardCount === 3) return 0.25;
    return Math.min(maxArc, Math.max(minArc, (cardCount - 1) * 0.1));
  })();

  const angleStep = ARC_WIDTH / (cardCount - 1 || 1);
  const hoverScale = 1.8;

  return (
    <group position={[-2.5, -9.7, 0]}>
      {cardIds.map((id, index) => {
        const angle = (index - middleIndex) * angleStep;
        const x = Math.sin(angle) * RADIUS;
        const baseY = Math.cos(angle) * RADIUS * 0.7;

        return (
          <group
            key={`${id}-${index}`}
            position={[x, baseY, -15 + index * 0.001]}
            rotation={[0, 0, -angle]}
          >
            <mesh
              position={[0, -CARD_HEIGHT / 2, 0]}
              onPointerOver={(e) => {
                e.stopPropagation();
                setHoveredIndex(index);
                document.body.style.cursor = 'pointer';
              }}
              onPointerOut={(e) => {
                e.stopPropagation();
                setHoveredIndex(null);
                document.body.style.cursor = 'default';
              }}
              onContextMenu={(e) => {
                e.stopPropagation();
                dispatch(showZoomedCard({cardId: id}));
              }}
              onClick={(e) => {
                e.stopPropagation();
                onCardSelect?.(id);
              }}
              onPointerDown={(e) => {
                e.stopPropagation();
                if (e.button === 2) {
                  dispatch(showZoomedCard({cardId: id}));
                }
              }}
            >
              <planeGeometry args={[CARD_WIDTH, CARD_HEIGHT]} />
              <meshBasicMaterial
                map={textures[index]}
                transparent={true}
                depthWrite={false}
                polygonOffset={true}
                polygonOffsetFactor={-1}
              />
            </mesh>
          </group>
        );
      })}

      {hoveredIndex !== null && (() => {
        const texture = textures[hoveredIndex];
        const angle = (hoveredIndex - middleIndex) * angleStep;
        const x = Math.sin(angle) * RADIUS;
        const y = 10.9;

        return (
          <group
            position={[x, y, -13]}
            rotation={[0, 0, 0]}
          >
            <mesh position={[0, -CARD_HEIGHT / 2, 0]} scale={hoverScale}>
              <planeGeometry args={[CARD_WIDTH, CARD_HEIGHT]} />
              <meshBasicMaterial
                map={texture}
                transparent={true}
                depthWrite={false}
                polygonOffset={true}
                polygonOffsetFactor={-1}
              />
            </mesh>
          </group>
        );
      })()}
    </group>
  );
};

export default CurrentPlayerHand;
