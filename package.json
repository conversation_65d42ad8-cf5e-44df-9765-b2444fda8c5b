{"name": "nextjs-15-clean-architecture-template", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:all": "concurrently \"npm run dev\" \"npm run convex:dev\"", "convex:dev": "convex dev", "convex:deploy": "convex deploy", "build": "next build", "start": "next start", "lint": "tsc --noEmit && next lint", "build:custom-rules": "npm --prefix eslint-plugin-custom-rules run build", "prelint": "npm run build:custom-rules", "lint:fix": "npx next lint --fix", "lint:plugin:dev": "npm install ./eslint-plugin-custom-rules && npm run lint", "deps:all": "npm run deps:screenshot && npm run deps:graph && npm run deps:github && npm run deps:server && npm run deps:client", "deps:github": "node tools/dependencies/github.js", "deps:graph": "npx depcruise . --include-only \"^src|^app\" --output-type dot | dot -T svg | depcruise-wrap-stream-in-html > tools/dependencies/index.html", "deps:screenshot": "npx depcruise . --include-only \"^src|^app\" --output-type dot | dot -T svg > assets/deps.svg", "deps:server": "npx depcruise . --include-only \"^src/server|^app\" --output-type dot | dot -T svg | depcruise-wrap-stream-in-html > tools/dependencies/server-dependency-graph.html", "deps:client": "npx depcruise . --include-only \"^src/client|^app\" --output-type dot | dot -T svg | depcruise-wrap-stream-in-html > tools/dependencies/client-dependency-graph.html", "pretest": "npm run build:custom-rules", "test": "vitest run --silent", "test:coverage": "vitest run --coverage", "seed:cards": "npx convex import --table cards src/server/Shared/infrastructure/seeds/cards.jsonl --replace", "import:cards": "ts-node --project tsconfig.tools.json tools/importCards/script.ts", "cy:run": "cypress run", "cy:open": "cypress open", "cy:local": "cypress open", "cy:ci": "cypress run --component --headless --browser chrome", "cy:ci:e2e": "cypress run --e2e --headless --browser chrome", "kill:all": "taskkill /f /im node.exe"}, "dependencies": {"@auth/core": "^0.37.0", "@convex-dev/auth": "^0.0.85", "@evyweb/ioctopus": "^1.2.0", "@evyweb/simple-ddd-toolkit": "^0.21.0", "@formkit/auto-animate": "^0.8.2", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/themes": "^3.2.1", "@react-three/drei": "^10.3.0", "@react-three/fiber": "^9.1.2", "@reduxjs/toolkit": "^2.8.2", "@tanstack/react-virtual": "^3.13.9", "@tsparticles/engine": "^3.8.1", "@tsparticles/react": "^3.0.0", "@tsparticles/slim": "^3.8.1", "animate.css": "^4.1.1", "clsx": "^2.1.1", "convex": "^1.23.0", "convex-helpers": "^0.1.83", "dotenv": "^16.5.0", "framer-motion": "^12.11.4", "lucide-react": "^0.511.0", "motion": "^12.11.4", "next": "^15.4.0", "next-intl": "^4.1.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-redux": "^9.2.0", "tailwind-merge": "^3.3.0", "three": "^0.177.0", "three-stdlib": "^2.36.0", "wawa-vfx": "^1.0.16"}, "devDependencies": {"@cypress/react": "^9.0.1", "@eslint/js": "^9.29.0", "@next/eslint-plugin-next": "^15.3.4", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/node": "^22.15.18", "@types/react": "^19.1.4", "@types/react-dom": "^19.1.5", "@types/three": "^0.177.0", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "@vitejs/plugin-react": "^4.4.1", "@vitest/coverage-v8": "^3.1.3", "@vitest/eslint-plugin": "^1.3.3", "concurrently": "^9.1.2", "convex-test": "^0.0.36", "cypress": "^14.4.1", "dependency-cruiser": "^16.10.2", "eslint": "^9.29.0", "eslint-config-next": "15.1.0", "eslint-plugin-boundaries": "^5.0.1", "eslint-plugin-clean-architecture": "^0.1.0", "eslint-plugin-custom-rules": "file:eslint-plugin-custom-rules", "eslint-plugin-jest": "^29.0.1", "jest-extended": "^4.0.2", "jsdom": "^25.0.1", "msw": "^2.8.2", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "ts-node": "^10.9.2", "typescript": "^5.7.2", "typescript-eslint": "^8.35.0", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.1.3", "vitest-mock-extended": "^2.0.2"}}